plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.sak.imgui_input'
    compileSdk 34

    defaultConfig {
        applicationId "com.sak.imgui_input"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags '-std=c++17'
            }
        }
    }

    buildFeatures {
        aidl true
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    def libsuVersion = '6.0.0'
    implementation "com.github.topjohnwu.libsu:core:${libsuVersion}"
    implementation "com.github.topjohnwu.libsu:service:${libsuVersion}"
    implementation "com.github.topjohnwu.libsu:nio:${libsuVersion}"
    implementation libs.appcompat
    implementation libs.material
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}