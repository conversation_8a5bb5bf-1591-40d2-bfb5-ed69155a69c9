//
// Created by admin on 2022/6/14.
//

#ifndef IMGUITESTMENU_PCH_H
#define IMGUITESTMENU_PCH_H

#include "EGL/egl.h"
#include "GLES3/gl3.h"
#include <string>
#include "imgui.h"
#include "imgui_impl_android.h"
#include "imgui_impl_opengl3.h"
#include "imgui_internal.h"
#include "stb_image.h"
#include <jni.h>
#include "android/native_window_jni.h"
#include "log.h"
#include <thread>
#include "android/asset_manager.h"
#include "android/asset_manager_jni.h"
#include "timer.h"
#endif //IMGUITESTMENU_PCH_H
